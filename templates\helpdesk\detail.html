{% extends "base.html" %}

{% block title %}Help desk - Footprints{% endblock %}

{% block head %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/event-detail-bundle.css') }}">
<style>
    .journey-detail-card {
        background: #fff;
        border-radius: 16px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        border: 1px solid #f1f3f4;
    }

    .section-header {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-bottom: 12px;
        padding-bottom: 8px;
        border-bottom: 1px solid #f8f9fa;
        flex-shrink: 0;
    }

    .section-header h1,
    .section-header h2 {
        font-size: 18px;
        font-weight: 700;
        color: #212529;
        margin: 0;
    }

    .event-title {
        font-size: 24px;
        font-weight: 700;
        color: #212529;
        margin: 0;
        line-height: 1.3;
    }

    .author-info,
    .event-meta {
        display: flex;
        gap: 8px;
    }

    .author-avatar {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        overflow: hidden;
        border: 2px solid #e9ecef;
    }

    .author-avatar img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .author-details {
        display: flex;
        flex-direction: column;
        gap: 1px;
    }

    .author-name {
        font-weight: 600;
        color: #212529;
        font-size: 13px;
    }

    .update-time {
        color: #6c757d;
        font-size: 11px;
        display: flex;
        align-items: center;
        gap: 4px;
    }

    .badge {
        font-size: 11px;
        font-weight: 600;
        border-radius: 12px;
        padding: 3px 10px;
    }

    .content-section {
        display: flex;
        flex-direction: column;
        min-height: 0;
        margin-bottom: 16px;
    }

    .section-content {
        color: #495057;
        line-height: 1.5;
        font-size: 14px;
        flex: 1;
        overflow: hidden;
    }

    .comments-section {
        display: flex;
        flex-direction: column;
        height: 100%;
    }

    .comments-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
        padding-bottom: 12px;
        border-bottom: 1px solid #e9ecef;
        flex-shrink: 0;
    }

    .comments-header h2 {
        display: flex;
        align-items: center;
        gap: 6px;
        font-size: 18px;
        font-weight: 600;
        color: #212529;
        margin: 0;
    }

    .comment-count {
        background: #f8f9fa;
        color: #495057;
        padding: 3px 10px;
        border-radius: 14px;
        font-size: 11px;
        font-weight: 600;
    }

    .comments-list {
        max-height: calc(100vh - 500px);
        overflow-y: auto;
    }

    .comment-item {
        display: flex;
        gap: 12px;
        padding: 16px 0;
        border-bottom: 1px solid #f8f9fa;
    }

    .comment-item:last-child {
        border-bottom: none;
    }

    .comment-avatar {
        width: 36px;
        height: 36px;
        border-radius: 50%;
        overflow: hidden;
        flex-shrink: 0;
    }

    .comment-avatar img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .comment-content {
        flex: 1;
    }

    .comment-header {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 6px;
    }

    .comment-author {
        font-weight: 600;
        color: #212529;
        font-size: 14px;
    }

    .comment-time {
        color: #6c757d;
        font-size: 12px;
    }

    .comment-text {
        color: #495057;
        font-size: 14px;
        line-height: 1.5;
        margin-bottom: 8px;
        word-break: break-word;
        overflow-wrap: break-word;
    }

    .comment-form-container {
        flex-shrink: 0;
        margin-top: 0;
    }

    .comment-form {
        display: flex;
        gap: 12px;
        align-items: center;
    }

    .comment-input {
        flex: 1;
        padding: 12px 16px;
        border: 1px solid #dee2e6;
        border-radius: 24px;
        font-size: 14px;
        outline: none;
        transition: border-color 0.2s;
        min-height: 40px;
    }

    .comment-input:focus {
        border-color: #007bff;
    }

    .submit-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        background: #007bff;
        color: white;
        border: none;
        border-radius: 50%;
        font-size: 16px;
        transition: background 0.2s;
    }

    .submit-btn:hover {
        background: #0056b3;
    }

    .empty-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 40px 20px;
        text-align: center;
        min-height: 200px;
    }

    .empty-state-icon {
        margin-bottom: 16px;
    }

    .empty-state-icon i {
        font-size: 48px;
        color: #dee2e6;
        opacity: 0.8;
    }

    .empty-state h4 {
        font-size: 18px;
        font-weight: 600;
        color: #6c757d;
        margin: 0 0 8px 0;
    }

    .empty-state p {
        font-size: 14px;
        color: #adb5bd;
        margin: 0;
        line-height: 1.5;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <!-- Back button -->
    <a href="#" id="smartBackButton"
        class="back-button d-inline-flex align-items-center text-decoration-none text-dark mb-3">
        <i class="bi bi-arrow-left me-2"></i>
        <span id="backButtonText">Back</span>
    </a>

    <div class="row g-4" id="ticketContainer">
        <div class="col-md-5 ticket-detail-panel">
            <div class="card shadow-sm border-0 rounded-3 h-100"
                style="max-height: calc(100vh - 400px); overflow-y: auto; height: 100%; min-height: calc(100vh - 500px);">
                <div class="card-body">
                    <div class="d-flex align-items-center gap-2 mb-2">
                        <div class="journey-breadcrumb mb-2">
                            <i class="bi bi-journal-bookmark"></i>
                            <span>Ticket Details</span>
                        </div>
                        <div class="ms-auto">
                            <div class="dropdown" {% if can_manage_content() and ticket.status not in
                                ['resolved', 'approved' , 'rejected' ] %}{% else %}style='display: none;' {% endif %}>
                                <button class="menu-btn" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="bi bi-three-dots-vertical"></i>
                                </button>
                                <ul class="dropdown-menu dropdown-menu-end shadow-sm">
                                    {% if ticket.user_id != g.current_user['id'] and ticket.assigned_to != session.user_id %}
                                    <li>
                                        <a class="dropdown-item modal-trigger" href="#" data-action="take"
                                            data-ticket-id="{{ ticket.id }}">
                                            <i class="bi bi-person-plus-fill me-2 text-success"></i>{% if ticket.assigned_to %}Take Over Ticket{% else %}Take Ticket{% endif %}
                                        </a>
                                    </li>
                                    <li>
                                        <hr class="dropdown-divider">
                                    </li>
                                    {% endif %}
                                    <li>
                                        <a class="dropdown-item modal-trigger" href="#" data-action="assign"
                                            data-ticket-id="{{ ticket.id }}">
                                            <i class="bi bi-person-plus me-2 text-dark"></i>{% if ticket.assigned_to
                                            %}Reassign Ticket{% else %}Assign Ticket{% endif %}
                                        </a>
                                    </li>
                                    {% if ticket.assigned_to and ticket.assigned_to == session.user_id and
                                    ticket.request_type != 'appeal' %}
                                    <li>
                                        <a class="dropdown-item modal-trigger" href="#" data-action="change"
                                            data-ticket-id="{{ ticket.id }}">
                                            <i class="bi bi-pencil-square me-2 text-dark"></i>Change Status
                                        </a>
                                    </li>
                                    {% endif %}
                                    {% if ticket.assigned_to and ticket.assigned_to == session.user_id %}
                                    <li>
                                        <a class="dropdown-item modal-trigger" href="#" data-action="drop"
                                            data-ticket-id="{{ ticket.id }}">
                                            <i class="bi bi-person-dash-fill me-2"></i>Drop Ticket
                                        </a>
                                    </li>
                                    {% endif %}
                                </ul>
                            </div>
                        </div>
                    </div>
                    <h1 class="event-title mb-1">{{ ticket.subject }}</h1>
                    <div class="content-section meta-section">
                        <div class="section-header" style="border-bottom: none; margin-bottom: 8px;">
                            <span
                                class="badge rounded-pill px-3 py-2 {% if ticket.status == 'new' %}bg-primary-subtle text-primary{% elif ticket.status == 'open' %}bg-success-subtle text-success{% elif ticket.status == 'stalled' %}bg-warning-subtle text-warning{% elif ticket.status == 'resolved' %}bg-info-subtle text-info{% elif ticket.status == 'approved' %}bg-success-subtle text-success{% elif ticket.status == 'rejected' %}bg-secondary-subtle text-secondary{% endif %}">
                                {% if ticket.status == 'new' %}<i class="bi bi-star me-1"></i>New{% elif ticket.status
                                == 'open' %}<i class="bi bi-unlock me-1"></i>Open{% elif ticket.status == 'stalled' %}<i
                                    class="bi bi-pause me-1"></i>Stalled{% elif ticket.status == 'resolved' %}<i
                                    class="bi bi-check2-circle me-1"></i>Resolved{% elif ticket.status == 'approved'
                                %}<i class="bi bi-check-lg me-1"></i>Approved{% elif ticket.status == 'rejected' %}<i
                                    class="bi bi-x-circle me-1"></i>Rejected{% endif %}
                            </span>
                            {% if ticket.assigned_to %}
                                {% if ticket.assigned_to == session.user_id %}
                                <!-- Assigned to current user - highlight in dark blue for distinction -->
                                <span class="badge rounded-pill bg-primary text-white px-3 py-2" style="min-width: 80px; text-align: center; font-size: 0.8em;">
                                    <i class="bi bi-person-check-fill me-1"></i>{{ ticket.assigned_username or 'You' }}
                                </span>
                                {% else %}
                                <!-- Assigned to other staff - use actual role from database -->
                                {% set assigned_user_role = ticket.assigned_user_role %}
                                <span class="badge rounded-pill {{ get_role_badge_class(assigned_user_role) }} border-0 px-3 py-2" style="min-width: 80px; text-align: center; font-size: 0.8em;">
                                    <i class="{{ get_role_icon(assigned_user_role) }} me-1"></i>{{ ticket.assigned_username }}
                                </span>
                                {% endif %}
                            {% endif %}
                        </div>
                        <div class="section-content">
                            <div class="event-meta">
                                <div class="author-info">
                                    <div class="author-avatar">
                                        <img src="{% if ticket.profile_image %}{{ url_for('static', filename='uploads/profile_images/' + ticket.profile_image) }}{% else %}{{ url_for('static', filename='uploads/profile_images/profile_placeholder.png') }}{% endif %}"
                                            alt="User Avatar"
                                            onerror="this.onerror=null; this.src='/static/uploads/profile_images/profile_placeholder.png'">
                                    </div>
                                    <div class="author-details">
                                        <span class="author-name">{{ ticket.username }}</span>
                                    </div>
                                </div>
                                <div class="update-time"><i class="bi bi-clock"></i>Created on {{
                                    ticket.created_at|datetime }}</div>
                            </div>
                        </div>
                    </div>
                    <div class="content-section description-section">
                        <div class="section-header">
                            <h3>Description</h3>
                        </div>
                        <div class="section-content"><span style="font-size: 14px; color: #495057;">{{
                                ticket.description
                                }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-7">
            <div class="card shadow-sm border-0 rounded-3"
                style="max-height: calc(100vh - 400px); overflow-y: auto; height: 100%;">
                <div class="card-body comments-section">
                    <div class="comments-header">
                        <h2><i class="bi bi-chat-dots"></i>Comments</h2>
                        <span class="comment-count">{{ ticket.replies|length }}</span>
                    </div>
                    <div class="comments-list">
                        {% if ticket.replies %}
                        {% for reply in ticket.replies %}
                        <div class="comment-item">
                            <div class="comment-avatar">
                                <img src="{% if reply.profile_image %}{{ url_for('static', filename='uploads/profile_images/' + reply.profile_image) }}{% else %}{{ url_for('static', filename='uploads/profile_images/profile_placeholder.png') }}{% endif %}"
                                    alt="User Avatar"
                                    onerror="this.onerror=null; this.src='/static/uploads/profile_images/profile_placeholder.png'">
                            </div>
                            <div class="comment-content">
                                <div class="comment-header">
                                    <span class="comment-author">{{reply.username}}</span>
                                    <span class="comment-time"><i class="bi bi-clock me-1"></i>{{
                                        reply.created_at|datetime }}</span>
                                </div>
                                <div class="comment-text">{{reply.content}}</div>
                            </div>
                        </div>
                        {% endfor %}
                        {% else %}
                        <div class="empty-state text-center w-100">
                            <div class="empty-state-icon">
                                <i class="bi bi-chat-left-dots"></i>
                            </div>
                            <h4>No comments yet</h4>
                            <p>Be the first to share your thoughts about this ticket!</p>
                        </div>
                        {% endif %}
                    </div>
                    {% if ticket.status in ['resolved', 'approved', 'rejected'] or (session.user_id != ticket.user_id and session.user_id != ticket.assigned_to) %}
                    <div class="p-4 rounded-3 mt-3 mb-0 bg-light">
                        <div class="d-flex align-items-start">
                            <i class="bi bi-info-circle fs-5 me-3 mt-1"></i>
                            <div>
                                {% if ticket.status in ['resolved', 'approved', 'rejected'] %}
                                <h6 class="fw-bold mb-1">Comments Disabled</h6>
                                <p class="mb-0 small">This ticket has been {{ ticket.status }} and no longer accepts new
                                    comments.</p>
                                {% elif session.user_id != ticket.user_id and session.user_id != ticket.assigned_to %}
                                <h6 class="fw-bold mb-1">Comments Restricted</h6>
                                <p class="mb-0 small">Only the ticket creator and assigned staff can add comments to
                                    this ticket.</p>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% else %}
                    <div class="comment-form-container mt-4">
                        <form action="{{url_for('helpdesk.add_reply')}}" id="post_comment" method="post"
                            class="comment-form">
                            <input type="hidden" name="ticket_id" value="{{ticket.id}}">
                            <input type="hidden" name="user_id" value="{{session['user_id']}}">
                            <!-- Preserve back parameter for navigation -->
                            {% if request.args.get('back') %}
                            <input type="hidden" name="back" value="{{ request.args.get('back') }}">
                            {% endif %}
                            <input type="text" class="comment-input" id="content" placeholder="Your Comment"
                                name="content" required>
                            <button type="submit" class="submit-btn"><i class="bi bi-send"></i></button>
                        </form>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- HERE -->
    <!-- Show admin response for appeals -->
    <div class="content-section status-section mt-4">
        <div class="section-header">
            <h3>Status & Appeal</h3>
        </div>
        <div class="section-content">
            <div class="status-body">
                <div class="row g-3">
                    <!-- Appeal Processing Section -->
                    {% if ticket.request_type == 'appeal' and (ticket.appeal_type == 'hidden_journey' or
                    ticket.appeal_type == 'sharing_block' or ticket.appeal_type == 'ban') and can_manage_content() %}
                    {% if ticket.assigned_to and ticket.status in ['new', 'open'] and ticket.user_id != session.user_id
                    and ticket.assigned_to != session.user_id %}
                    <div class="rounded-3">
                        <div class="d-flex align-items-center">
                            <div>
                                <h6 class="fw-bold mb-1 text-primary">Appeal Assigned</h6>
                                <p class="mb-0 small">This appeal is assigned to {{ ticket.get('assigned_username',
                                    ticket.staff_name) }}. Only the assigned staff member can process this appeal.
                                </p>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    {% if ticket.status in ['new', 'open'] and ticket.assigned_to == session.user_id %}
                    <div class="rounded-3">
                        <div class="d-flex align-items-start">
                            <div>
                                <h6 class="fw-bold mb-1">
                                    {% if ticket.appeal_type == 'hidden_journey' %}
                                    Journey Appeal
                                    {% elif ticket.appeal_type == 'sharing_block' %}
                                    Blocked User Appeal
                                    {% elif ticket.appeal_type == 'ban' %}
                                    Ban Appeal
                                    {% else %}
                                    Appeal
                                    {% endif %}
                                </h6>
                                <p class="mb-0 small">
                                    {% if ticket.appeal_type == 'hidden_journey' %}
                                    This appeal is assigned to you. You can approve to unhide the journey or reject
                                    with a reason.
                                    {% elif ticket.appeal_type == 'sharing_block' %}
                                    This appeal is assigned to you. You can approve to unblock the user or reject
                                    with a reason.
                                    {% elif ticket.appeal_type == 'ban' %}
                                    This appeal is assigned to you. You can approve to unban the user or reject with
                                    a reason.
                                    {% else %}
                                    This appeal is assigned to you. Review and decide whether to approve or reject
                                    it.
                                    {% endif %}
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-success btn-sm rounded-pill appeal-action-btn py-2 px-3"
                            data-action="approve" data-appeal-id="{{ ticket.id }}">
                            <i class="bi bi-check-lg me-1"></i>Approve Appeal
                        </button>
                        <button type="button" class="btn btn-danger btn-sm rounded-pill appeal-action-btn py-2 px-3"
                            data-action="reject" data-appeal-id="{{ ticket.id }}">
                            <i class="bi bi-x-lg me-1"></i>Reject Appeal
                        </button>
                    </div>

                    {% endif %}
                    <div class="col-lg-5 col-md-5 mb-3">
                        {% if ticket.status in ['new', 'open'] %}
                        {% elif ticket.status == 'approved' %}
                        <h6 class="text-uppercase text-muted small fw-bold"><i
                                class="bi bi-info-circle text-dark me-1"></i>Result
                        </h6>
                        <div class="rounded-3">
                            <div class="d-flex align-items-start">
                                <div>
                                    <h6 class="fw-bold mb-1 text-success">Appeal Approved</h6>
                                    <p class="mb-0 small">This appeal has been approved and the journey has been
                                        unhidden.</p>
                                </div>
                            </div>
                        </div>
                        {% elif ticket.status == 'rejected' %}
                        <h6 class="text-uppercase text-muted small fw-bold"><i
                                class="bi bi-info-circle text-success me-1"></i>Result
                        </h6>
                        <div class="rounded-3">
                            <div class="d-flex align-items-start">
                                <div>
                                    <h6 class="fw-bold mb-1 text-danger">Appeal Rejected</h6>
                                    <p class="mb-0 small">This appeal has been reviewed and rejected.</p>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                    </div>

                    <!-- Reason -->
                    {% if ticket.request_type == 'appeal' and ticket.admin_response %}
                    <div class="col-lg-7 col-md-7">
                        <h6 class="text-uppercase text-muted small fw-bold">
                            {% if ticket.status == 'approved' %}
                            <i class="bi bi-check-circle text-success me-1"></i>Staff Response
                            {% elif ticket.status == 'rejected' %}
                            <i class="bi bi-x-circle text-danger me-1"></i>Rejection Reason
                            {% else %}
                            <i class="bi bi-chat-square-text me-1"></i>Staff Response
                            {% endif %}
                        </h6>
                        <div class="rounded-3"
                            style="max-height: calc(100vh - 750px); overflow-y: auto; overflow-x: hidden; max-width: 100%; white-space: normal; word-break: break-word; padding: 0; margin: 0;">
                            <p class="mb-0" style="margin:0; padding:0;">
                                {{ ticket.admin_response }}
                            </p>
                        </div>
                    </div>
                    {% endif %}
                    {% endif %}
                </div>
                {% if ticket.status == 'stalled' %}
                <div class="rounded-3 mt-3 mb-3">
                    <div class="d-flex align-items-start">
                        <div>
                            <h6 class="fw-bold mb-1 text-warning">Ticket Stalled</h6>
                            <p class="mb-0 small">This ticket has been stalled.</p>
                        </div>
                    </div>
                </div>
                {% elif ticket.status == 'resolved' %}
                <div class="rounded-3 mt-3 mb-3">
                    <div class="d-flex align-items-start">
                        <div>
                            <h6 class="fw-bold mb-1 text-success">Ticket Resolved</h6>
                            <p class="mb-0 small">This ticket has been reviewed and resolved.</p>
                        </div>
                    </div>
                </div>
                {% elif ticket.status == 'new' %}
                <div class="rounded-3 mt-3 mb-3">
                    <div class="d-flex align-items-start">
                        <div>
                            <h6 class="fw-bold mb-1 text-success">Ticket New</h6>
                            <p class="mb-0 small">This ticket is currently new and waiting for a staff member to
                                process it. Use "Take Ticket" or "Assign Ticket" above.</p>
                        </div>
                    </div>
                </div>
                {% elif ticket.status == 'open' and ticket.request_type != 'appeal' %}
                {% if not ticket.assigned_to %}
                <div class="rounded-3 mt-3 mb-3">
                    <div class="d-flex align-items-start">
                        <div>
                            <h6 class="fw-bold mb-1 text-success">Ticket Open</h6>
                            <p class="mb-0 small">This ticket is currently open and waiting for a staff member to
                                process it. Use "Take Ticket" or "Assign Ticket" above.</p>
                        </div>
                    </div>
                </div>
                {% else %}
                <div class="rounded-3 mt-3 mb-3">
                    <div class="d-flex align-items-start">
                        <div>
                            <h6 class="fw-bold mb-1 text-success">Ticket Open</h6>
                            <p class="mb-0 small">This ticket is currently assigned to {{
                                ticket.get('assigned_username',
                                ticket.staff_name) }}.</p>
                        </div>
                    </div>
                </div>
                {% endif %}
                {% endif %}
            </div>
        </div>
    </div>
    <div id="assignTicketFormTemplate" style="display:none;">
        <form id="assignTicketForm" method="POST" action="{{ url_for('helpdesk.assign_ticket') }}">
            <input type="hidden" name="ticket_id" value="{{ ticket.id }}">
            <!-- Preserve back parameter for navigation -->
            {% if request.args.get('back') %}
            <input type="hidden" name="back" value="{{ request.args.get('back') }}">
            {% endif %}
            <div class="mb-3">
                <label for="staff_id" class="form-label">Assign to Staff Member</label>
                <select class="form-select" name="staff_id" required style="max-width: 500px;">
                    <option value="" disabled {% if not ticket.assigned_to %}selected{% endif %}>Select staff member
                    </option>
                    {% for staff in staff_list %}
                    {# Only show staff members (editor, support_tech, admin), exclude current user, and exclude currently assigned staff #}
                    {% if staff.role in ['editor', 'support_tech', 'admin'] and staff.id != session.user_id and staff.id != ticket.assigned_to %}
                    <option value="{{staff.id}}">
                        <span class="badge {{ get_role_badge_class(staff.role) }}">
                            <i class="{{ get_role_icon(staff.role) }}"></i>
                        </span>
                        {{staff.username}} ({{staff.role|title}})
                    </option>
                    {% endif %}
                    {% endfor %}
                </select>
                <div class="form-text">
                    <i class="bi bi-info-circle me-1"></i>
                    {% if ticket.assigned_to %}
                    Reassigning this ticket will notify both the user and the new assignee. Use "Take Ticket" to assign to yourself.
                    {% else %}
                    Only staff members can be assigned tickets. Use "Take Ticket" to assign to yourself.
                    {% endif %}
                </div>
            </div>
        </form>
    </div>
    <div id="changeStatusFormTemplate" style="display:none;">
        <form id="changeStatusForm" method="POST" action="{{ url_for('helpdesk.change_ticket_status') }}">
            <input type="hidden" name="ticket_id" value="{{ ticket.id }}">
            <!-- Preserve back parameter for navigation -->
            {% if request.args.get('back') %}
            <input type="hidden" name="back" value="{{ request.args.get('back') }}">
            {% endif %}

            <!-- Current Status Display -->
            <div class="mb-3">
                <label class="form-label fw-medium">Current Status</label>
                <div class="current-status-display">
                    <span
                        class="badge rounded-pill status-badge {% if ticket.status == 'new' %}bg-primary-subtle text-primary{% elif ticket.status == 'open' %}bg-success-subtle text-success{% elif ticket.status == 'stalled' %}bg-warning-subtle text-warning{% elif ticket.status == 'resolved' %}bg-info-subtle text-info{% endif %} px-3 py-2">
                        {% if ticket.status == 'new' %}<i class="bi bi-star me-1"></i>New{% elif ticket.status == 'open'
                        %}<i class="bi bi-unlock me-1"></i>Open{% elif ticket.status == 'stalled' %}<i
                            class="bi bi-pause me-1"></i>Stalled{% elif ticket.status == 'resolved' %}<i
                            class="bi bi-check2-circle me-1"></i>Resolved{% endif %}
                    </span>
                </div>
            </div>

            <!-- New Status Selection -->
            <div class="mb-3">
                <label for="status" class="form-label fw-medium">Change Status To</label>
                <select class="form-control status-change-select" name="status" id="statusSelect">
                    {% if ticket.status == 'new' %}
                    <!-- From 'new': can go to 'open' or 'stalled' -->
                    <option value="">-- Select New Status --</option>
                    <option value="open">
                        <i class="bi bi-unlock"></i> Open - Start working on the ticket
                    </option>
                    <option value="stalled">
                        <i class="bi bi-pause"></i> Stalled - Waiting for information or resources
                    </option>
                    {% elif ticket.status == 'open' %}
                    <!-- From 'open': can go to 'stalled' or 'resolved' -->
                    <option value="">-- Select New Status --</option>
                    <option value="stalled">
                        <i class="bi bi-pause"></i> Stalled - Waiting for information or resources
                    </option>
                    <option value="resolved">
                        <i class="bi bi-check2-circle"></i> Resolved - Issue has been fixed
                    </option>
                    {% elif ticket.status == 'stalled' %}
                    <!-- From 'stalled': can go back to 'open' or forward to 'resolved' -->
                    <option value="">-- Select New Status --</option>
                    <option value="open">
                        <i class="bi bi-unlock"></i> Open - Resume working on the ticket
                    </option>
                    <option value="resolved">
                        <i class="bi bi-check2-circle"></i> Resolved - Issue has been fixed
                    </option>
                    {% elif ticket.status == 'resolved' %}
                    <!-- From 'resolved': can reopen if needed -->
                    <option value="">-- Select New Status --</option>
                    <option value="open">
                        <i class="bi bi-unlock"></i> Open - Reopen the ticket
                    </option>
                    <option value="stalled">
                        <i class="bi bi-pause"></i> Stalled - Needs additional work
                    </option>
                    {% endif %}
                </select>

                <!-- Status Change Guidelines -->
                <div class="form-text mt-2">
                    {% if ticket.status == 'new' %}
                    <small class="text-muted">
                        <i class="bi bi-info-circle me-1"></i>
                        Choose <strong>Open</strong> to start working on this ticket, or <strong>Stalled</strong> if you
                        need more information.
                    </small>
                    {% elif ticket.status == 'open' %}
                    <small class="text-muted">
                        <i class="bi bi-info-circle me-1"></i>
                        Choose <strong>Resolved</strong> when the issue is fixed, or <strong>Stalled</strong> if you're
                        waiting for something.
                    </small>
                    {% elif ticket.status == 'stalled' %}
                    <small class="text-muted">
                        <i class="bi bi-info-circle me-1"></i>
                        Choose <strong>Open</strong> to resume work, or <strong>Resolved</strong> if the issue is now
                        fixed.
                    </small>
                    {% elif ticket.status == 'resolved' %}
                    <small class="text-muted">
                        <i class="bi bi-info-circle me-1"></i>
                        You can reopen this ticket if additional work is needed.
                    </small>
                    {% endif %}
                </div>
            </div>

            <!-- Status Change Confirmation -->
            <div id="statusChangePreview" class="alert status-preview rounded-3 mb-3" style="display: none;">
                <div class="d-flex align-items-center">
                    <i class="bi bi-arrow-right-circle fs-5 me-3 text-primary"></i>
                    <div>
                        <small class="fw-medium text-dark">Status will change from:</small><br>
                        <span class="badge status-badge bg-secondary me-2" id="currentStatusBadge">
                            {% if ticket.status == 'new' %}<i class="bi bi-star me-1"></i>{{ ticket.status|title }}{%
                            elif ticket.status == 'open' %}<i class="bi bi-unlock me-1"></i>{{ ticket.status|title }}{%
                            elif ticket.status == 'stalled' %}<i class="bi bi-pause me-1"></i>{{ ticket.status|title
                            }}{% elif ticket.status == 'resolved' %}<i class="bi bi-check2-circle me-1"></i>{{
                            ticket.status|title }}{% else %}{{ ticket.status|title }}{% endif %}
                        </span>
                        <i class="bi bi-arrow-right mx-1 status-transition-arrow"></i>
                        <span class="badge status-badge bg-primary" id="newStatusBadge"></span>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <div id="takeTicketFormTemplate" style="display:none;">
        <form id="takeTicketForm" method="POST" action="{{ url_for('helpdesk.take_ticket') }}">
            <input type="hidden" name="ticket_id" value="{{ ticket.id }}">
            <!-- Preserve back parameter for navigation -->
            {% if request.args.get('back') %}
            <input type="hidden" name="back" value="{{ request.args.get('back') }}">
            {% endif %}
            
            {% if ticket.assigned_to %}
            <!-- Warning for taking over an assigned ticket -->
            <div class="alert alert-warning rounded-3 mb-3">
                <div class="d-flex align-items-start">
                    <i class="bi bi-exclamation-triangle-fill fs-5 me-3 mt-1"></i>
                    <div>
                        <h6 class="fw-bold mb-1">Take Over Ticket</h6>
                        <p class="mb-0 small">This ticket is currently assigned to <strong>{{ ticket.get('assigned_username', ticket.staff_name) }}</strong>. Taking over will reassign it to you and notify the previous assignee.</p>
                    </div>
                </div>
            </div>
            <p class="text-warning"><strong>⚠️ Are you sure you want to take over this ticket from {{ ticket.get('assigned_username', ticket.staff_name) }}?</strong></p>
            {% else %}
            <!-- Normal take ticket message -->
            <div class="alert alert-success rounded-3 mb-3">
                <div class="d-flex align-items-start">
                    <i class="bi bi-person-plus-fill fs-5 me-3 mt-1"></i>
                    <div>
                        <h6 class="fw-bold mb-1">Take Ticket</h6>
                        <p class="mb-0 small">This will assign the ticket to you and change its status to "open".</p>
                    </div>
                </div>
            </div>
            <p>Are you sure you want to take this ticket?</p>
            {% endif %}
        </form>
    </div>

    <div id="dropTicketFormTemplate" style="display:none;">
        <form id="dropTicketForm" method="POST" action="{{ url_for('helpdesk.drop_ticket') }}">
            <input type="hidden" name="ticket_id" value="{{ ticket.id }}">
            <!-- Preserve back parameter for navigation -->
            {% if request.args.get('back') %}
            <input type="hidden" name="back" value="{{ request.args.get('back') }}">
            {% endif %}
            <div class="alert alert-warning rounded-3 mb-3">
                <div class="d-flex align-items-start">
                    <i class="bi bi-person-dash-fill fs-5 me-3 mt-1"></i>
                    <div>
                        <h6 class="fw-bold mb-1">Drop Ticket</h6>
                        <p class="mb-0 small">This will unassign the ticket and return it to the queue for other staff
                            to
                            take.</p>
                    </div>
                </div>
            </div>
            <p>Are you sure you want to drop this ticket?</p>
        </form>
    </div>

    <!-- Appeal Processing Modal Templates -->
    <div id="approveAppealFormTemplate" style="display:none;">
        <form id="approveAppealForm" method="POST"
            action="{{ url_for('helpdesk.process_appeal', appeal_id=ticket.id) }}">
            <input type="hidden" name="action" value="approve">
            <!-- Preserve back parameter for navigation -->
            {% if request.args.get('back') %}
            <input type="hidden" name="back" value="{{ request.args.get('back') }}">
            {% endif %}
            <div class="alert alert-success rounded-3 mb-3">
                <div class="d-flex align-items-center justify-content-center">
                    <i class="bi bi-check-circle fs-5 me-3 mt-1"></i>
                    <div>
                        {% if ticket.appeal_type == 'hidden_journey' %}
                        This will unhide the journey and make it visible to other users again.
                        {% elif ticket.appeal_type == 'sharing_block' %}
                        This will unblock the user and allow them to share journeys again.
                        {% elif ticket.appeal_type == 'ban' %}
                        This will unban the user and allow them to log in again.
                        {% else %}
                        This will approve the appeal and take the appropriate action.
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="mb-3">
                <label for="approve_response" class="form-label">Response to User (Optional)</label>
                <textarea class="form-control" id="approve_response" name="response" rows="3"></textarea>
                <div class="form-text">This message will be sent to the user as a notification.</div>
            </div>
        </form>
    </div>

    <div id="rejectAppealFormTemplate" style="display:none;">
        <form id="rejectAppealForm" method="POST"
            action="{{ url_for('helpdesk.process_appeal', appeal_id=ticket.id) }}">
            <input type="hidden" name="action" value="reject">
            <!-- Preserve back parameter for navigation -->
            {% if request.args.get('back') %}
            <input type="hidden" name="back" value="{{ request.args.get('back') }}">
            {% endif %}
            <div class="alert alert-danger rounded-3 mb-3">
                <div class="d-flex align-items-center justify-content-center">
                    <i class="bi bi-x-circle fs-5 me-3"></i>
                    <div>
                        {% if ticket.appeal_type == 'hidden_journey' %}
                        The journey will remain hidden. Please provide a clear explanation.
                        {% elif ticket.appeal_type == 'sharing_block' %}
                        The user will remain blocked from sharing. Please provide a clear explanation.
                        {% elif ticket.appeal_type == 'ban' %}
                        The user will remain banned from the system. Please provide a clear explanation.
                        {% else %}
                        The appeal will be rejected. Please provide a clear explanation.
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="mb-3">
                <label for="reject_response" class="form-label">Reason for Rejection <span
                        class="text-danger">*</span></label>
                <textarea class="form-control" id="reject_response" name="response" rows="4" required "></textarea>
                <div class=" form-text">This explanation will be sent to the user. Be specific and constructive.</div>
            </div>
        </form>
    </div>
</div>
<script>
    // Global variables for role checking
    const canManageContent = {% if can_manage_content() %}true{% else %}false{% endif %};
    const isLoggedIn = {% if g.current_user %}true{% else %}false{% endif %};

    // Simple smart back button - always uses direct navigation, no browser history
    function smartBack() {
        try {
            // Method 1: Check for explicit back URL parameter (most reliable)
            const urlParams = new URLSearchParams(window.location.search);
            const backUrl = urlParams.get('back');

            console.log('Smart back debug:');
            console.log('- Current URL:', window.location.href);
            console.log('- Back URL parameter:', backUrl);

            if (backUrl) {
                // Try to use the URL directly first (it might already be properly formatted)
                let targetUrl = backUrl;

                // If it looks like it needs decoding, decode it
                if (backUrl.includes('%')) {
                    targetUrl = decodeURIComponent(backUrl);
                }

                console.log('- Target URL:', targetUrl);

                // Only allow internal URLs for security
                if (targetUrl.startsWith('/') || targetUrl.startsWith(window.location.origin)) {
                    console.log('- Using URL parameter method, navigating to:', targetUrl);
                    // Always use direct navigation - never use history.back()
                    window.location.href = targetUrl;
                    return;
                }
            }

            // Method 2: Check referrer to determine origin (for initial navigation only)
            const referrer = document.referrer;
            console.log('- Referrer:', referrer);
            console.log('- Current pathname:', window.location.pathname);

            if (referrer && !referrer.includes(window.location.pathname)) {
                // Only use referrer if it's not the current page (avoids form submission loops)
                console.log('- Using referrer method');

                // Priority 1: If from notifications, go directly to notifications
                if (referrer.includes('/notifications')) {
                    console.log('- Referrer method: going to notifications');
                    window.location.href = '{{ url_for("main.view_all_notifications") }}';
                    return;
                }

                // Priority 2: If from helpdesk management, go directly to helpdesk management
                if (referrer.includes('/helpdesk/manage')) {
                    console.log('- Referrer method: going to helpdesk management');
                    window.location.href = '{{ url_for("helpdesk.get_tickets") }}';
                    return;
                }

                // Priority 3: If from my helpdesk tickets, go directly to my helpdesk tickets
                if (referrer.includes('/helpdesk') && !referrer.includes('/helpdesk/ticket')) {
                    console.log('- Referrer method: going to my helpdesk tickets');
                    window.location.href = '{{ url_for("helpdesk.get_user_tickets") }}';
                    return;
                }
            }

            // Method 3: Direct fallback based on user permissions (NO browser history)
            console.log('- Using permission-based fallback');
            console.log('- isLoggedIn:', isLoggedIn, 'canManageContent:', canManageContent);
            if (isLoggedIn) {
                if (canManageContent) {
                    console.log('- Fallback: going to helpdesk management');
                    window.location.href = '{{ url_for("helpdesk.get_tickets") }}';
                } else {
                    console.log('- Fallback: going to my helpdesk tickets');
                    window.location.href = '{{ url_for("helpdesk.get_user_tickets") }}';
                }
            } else {
                console.log('- Fallback: going to landing page');
                window.location.href = '{{ url_for("main.get_landing_page") }}';
            }
        } catch (error) {
            // If anything fails, use a safe fallback with direct navigation
            console.error('Smart back button error:', error);
            if (isLoggedIn && canManageContent) {
                window.location.href = '{{ url_for("helpdesk.get_tickets") }}';
            } else if (isLoggedIn) {
                window.location.href = '{{ url_for("helpdesk.get_user_tickets") }}';
            } else {
                window.location.href = '{{ url_for("main.get_landing_page") }}';
            }
        }
    }

    // Simple back button text update
    function updateBackButtonText() {
        const backButtonText = document.getElementById('backButtonText');

        // Method 1: Check for back URL parameter first (most reliable)
        const urlParams = new URLSearchParams(window.location.search);
        const backUrl = urlParams.get('back');

        if (backUrl) {
            // Determine context from back URL - prioritize notifications first
            if (backUrl.includes('/notifications')) {
                backButtonText.textContent = 'Back to Notifications';
            } else if (backUrl.includes('/helpdesk/manage')) {
                backButtonText.textContent = 'Back to Helpdesk Management';
            } else if (backUrl.includes('/helpdesk')) {
                backButtonText.textContent = 'Back to My Helpdesk Tickets';
            } else {
                backButtonText.textContent = 'Back';
            }
            return;
        }

        // Method 2: Check referrer to determine origin
        const referrer = document.referrer;
        if (referrer && referrer !== window.location.href) {
            // Priority 1: If from notifications, show notifications
            if (referrer.includes('/notifications')) {
                backButtonText.textContent = 'Back to Notifications';
                return;
            }

            // Priority 2: If from helpdesk management, show helpdesk management
            if (referrer.includes('/helpdesk/manage')) {
                backButtonText.textContent = 'Back to Helpdesk Management';
                return;
            }

            // Priority 3: If from my helpdesk tickets, show my helpdesk tickets
            if (referrer.includes('/helpdesk') && !referrer.includes('/helpdesk/ticket')) {
                backButtonText.textContent = 'Back to My Helpdesk Tickets';
                return;
            }
        }

        // Method 3: Default fallback based on user permissions
        if (isLoggedIn) {
            if (canManageContent) {
                backButtonText.textContent = 'Back to Helpdesk Management';
            } else {
                backButtonText.textContent = 'Back to My Helpdesk Tickets';
            }
        } else {
            backButtonText.textContent = 'Back';
        }
    }

    // Status change preview functionality
    function initializeStatusChangePreview() {
        const statusSelect = document.querySelector('#statusSelect');
        const previewDiv = document.querySelector('#statusChangePreview');
        const newStatusBadge = document.querySelector('#newStatusBadge');

        // Status mapping for display
        const statusMapping = {
            'new': { label: 'New', icon: 'bi-star', class: 'bg-primary-subtle text-primary' },
            'open': { label: 'Open', icon: 'bi-unlock', class: 'bg-success-subtle text-success' },
            'stalled': { label: 'Stalled', icon: 'bi-pause', class: 'bg-warning-subtle text-warning' },
            'resolved': { label: 'Resolved', icon: 'bi-check2-circle', class: 'bg-info-subtle text-info' }
        };

        if (statusSelect && previewDiv && newStatusBadge) {
            statusSelect.addEventListener('change', function () {
                const selectedStatus = this.value;

                // Clear any previous validation styling
                this.classList.remove('is-invalid');

                if (selectedStatus && statusMapping[selectedStatus]) {
                    const statusInfo = statusMapping[selectedStatus];
                    newStatusBadge.innerHTML = `<i class="${statusInfo.icon} me-1"></i>${statusInfo.label}`;
                    newStatusBadge.className = `badge ${statusInfo.class}`;
                    previewDiv.style.display = 'block';
                } else {
                    previewDiv.style.display = 'none';
                }
            });
        }
    }

    document.addEventListener('DOMContentLoaded', function () {
        // Update back button text on page load
        updateBackButtonText();

        // Add event listener for smart back button
        const backButton = document.getElementById('smartBackButton');
        if (backButton) {
            backButton.addEventListener('click', function(e) {
                e.preventDefault();
                smartBack();
            });
        }

        // Existing modal functionality
        document.querySelectorAll('.modal-trigger').forEach(btn => {
            btn.addEventListener('click', function (e) {
                e.preventDefault();
                const action = this.getAttribute('data-action');
                let formHtml, title, actionText, actionClass;
                if (action === 'assign') {
                    formHtml = document.getElementById('assignTicketFormTemplate').innerHTML;
                    title = '{{ ticket.assigned_to and "Reassign Ticket" or "Assign Ticket" }}';
                    actionText = 'Assign';
                    actionClass = 'btn-primary';
                } else if (action === 'change') {
                    formHtml = document.getElementById('changeStatusFormTemplate').innerHTML;
                    title = 'Change Status';
                    actionText = 'Update';
                    actionClass = 'btn-primary';
                } else if (action === 'take') {
                    formHtml = document.getElementById('takeTicketFormTemplate').innerHTML;
                    title = '{% if ticket.assigned_to %}Take Over Ticket{% else %}Take Ticket{% endif %}';
                    actionText = '{% if ticket.assigned_to %}Take Over{% else %}Take{% endif %}';
                    actionClass = '{% if ticket.assigned_to %}btn-warning{% else %}btn-success{% endif %}';
                } else if (action === 'drop') {
                    formHtml = document.getElementById('dropTicketFormTemplate').innerHTML;
                    title = 'Drop Ticket';
                    actionText = 'Drop';
                    actionClass = 'btn-warning';
                }
                showModal(title, formHtml, {
                    actionText: actionText,
                    actionClass: actionClass,
                    onAction: function () {
                        const form = document.querySelector('#commonModal form');
                        if (form) {
                            // Validate status change form
                            if (action === 'change') {
                                const statusSelect = form.querySelector('select[name="status"]');
                                if (!statusSelect.value) {
                                    statusSelect.focus();
                                    statusSelect.classList.add('is-invalid');
                                    return false;
                                }
                            }
                            form.submit();
                        }
                    },
                    onShow: function () {
                        // Initialize status change functionality when modal is shown
                        if (action === 'change') {
                            initializeStatusChangePreview();
                        }
                    }
                });
            });
        });

        // Appeal processing functionality
        document.querySelectorAll('.appeal-action-btn').forEach(btn => {
            btn.addEventListener('click', function (e) {
                e.preventDefault();
                const action = this.getAttribute('data-action');
                const appealId = this.getAttribute('data-appeal-id');

                let formHtml, title, actionText, actionClass;

                if (action === 'approve') {
                    formHtml = document.getElementById('approveAppealFormTemplate').innerHTML;
                    title = 'Approve Appeal';
                    actionText = 'Approve';
                    actionClass = 'btn-success';
                } else if (action === 'reject') {
                    formHtml = document.getElementById('rejectAppealFormTemplate').innerHTML;
                    title = 'Reject Appeal';
                    actionText = 'Reject';
                    actionClass = 'btn-danger';
                }

                showModal(title, formHtml, {
                    actionText: actionText,
                    actionClass: actionClass,
                    onAction: function () {
                        const form = document.querySelector('#commonModal form');
                        if (form) {
                            // Validate required fields for reject action
                            if (action === 'reject') {
                                const responseField = form.querySelector('textarea[name="response"]');
                                if (!responseField.value.trim()) {
                                    responseField.focus();
                                    responseField.classList.add('is-invalid');
                                    return false;
                                }
                            }
                            form.submit();
                        }
                    }
                });
            });
        });


    });
</script>
{% endblock %}